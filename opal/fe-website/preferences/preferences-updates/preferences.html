<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preferences Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px; /* Increase space between form groups */
        }
        label {
            display: block; /* Make labels block elements */
            margin: 10px 0; /* Add margin above and below labels */
        }
        textarea {
            width: 100%; /* Full width for textareas */
            padding: 10px; /* Add padding inside textareas */
            margin-top: 5px; /* Space above textareas */
            border: 1px solid #ccc; /* Border for textareas */
            border-radius: 4px; /* Rounded corners */
        }
        #mentee-questions {
            display: none; /* Hide initially */ 
        }
    </style>
</head>
<body>

<h1>Preferences Form</h1>
<form id="userForm">
    <div class="form-group">
        <label for="name">What is your name?</label>
        <textarea name="fullName" id="fullName" rows="1" placeholder="Your answer..."></textarea>

        <label for="pronouns">What are your pronouns?</label>
        <textarea name="pronouns" id="pronouns" rows="1" placeholder="Your answer..."></textarea>

        <label for="program">Which program are you participating in?</label>
        <select name="program" name="cohort_names" id="state" style="width: 100%;">
            <option value="">Select a state</option>
            <option value="Engineers">Engineers</option>
            <option value="Designers">Designers</option>
        </select>

        <label for="ethnicity">Ethnicity</label>
        <select name="ethnicity" id="ethnicity" style="width: 100%;" multiple>
            <option value="">Select an ethncity</option>
            <option value="asian">Asian</option>
            <option value="black">Black</option>
            <option value="white">White</option>
        </select>

        <label for="gender">Gender</label>
        <select name="gender" id="gender" style="width: 100%;" multiple>
            <option value="">Select a gender</option>
            <option value="cisgender_woman">Cisgender Woman</option>
            <option value="cisgender_man">Cisgender Man</option>
            <option value="transgender_man">Transgender Man</option>
            <option value="transgender_woman">Transgender Woman</option>
        </select>
        
        <label for="role">Are you a mentee or a mentor?</label>
        <select id="role" onchange="showAdditionalQuestions()">
            <option value="">Select an option</option>
            <option name="mentee_status" value="mentee">Mentee</option>
            <option name="mentor_status" value="mentor">Mentor</option> 
        </select>    
    </div>

    <div class="form-group" id="mentee-questions">
        <label for="goal">What is your biggest goal?</label>
        <textarea id="goal" rows="4" placeholder="Your answer..."></textarea>

        <label for="challenges">What challenges are you facing?</label>
        <textarea id="challenges" rows="4" placeholder="Your answer..."></textarea>

        <label for="expectations">What are your expectations from the mentorship?</label>
        <textarea id="expectations" rows="4" placeholder="Your answer..."></textarea>

        <label>Which industry/ies would you like to learn more about?</label>
        <textarea name="industries" rows="4" placeholder="Your answer..."></textarea>

        <label>Please list job role(s) you're interested in.</label>
        <textarea name="job_titles" rows="4" placeholder="Your answer..."></textarea>

        <label>Which professional development skills would you like to grow during this mentorship?</label>
        <textarea name="industries_professional" rows="4" placeholder="Your answer..."></textarea>

        <label>What are your personal interests outside of work or school?</label>
        <textarea name="interests_personal" rows="4" placeholder="Your answer..."></textarea>

        <label>What are your custom interests outside of work or school?</label>
        <textarea name="custom_cohort" rows="4" placeholder="Your answer..."></textarea>
    </div>

    <div class="form-group" id="mentor-question" style="display: none;">
        <label for="reason">Why do you want to mentor?</label>
        <textarea id="reason" rows="4" placeholder="Your answer..."></textarea>
        
        <label for="state">Where are you from?</label> <!-- Question with Searchable Dropdown! -->
        <select id="state" style="width: 100%;">
            <option value="">Select a state</option>
            <option value="AL">Alabama</option>
            <option value="AK">Alaska</option>
            <option value="AZ">Arizona</option>
            <option value="AR">Arkansas</option>
            <option value="CA">California</option>
            <option value="CO">Colorado</option>
            <option value="CT">Connecticut</option>
            <option value="DE">Delaware</option>
            <option value="FL">Florida</option>
            <option value="GA">Georgia</option>
            <option value="HI">Hawaii</option>
            <option value="ID">Idaho</option>
            <option value="IL">Illinois</option>
            <option value="IN">Indiana</option>
            <option value="IA">Iowa</option>
            <option value="KS">Kansas</option>
            <option value="KY">Kentucky</option>
            <option value="LA">Louisiana</option>
            <option value="ME">Maine</option>
            <option value="MD">Maryland</option>
            <option value="MA">Massachusetts</option>
            <option value="MI">Michigan</option>
            <option value="MN">Minnesota</option>
            <option value="MS">Mississippi</option>
            <option value="MO">Missouri</option>
            <option value="MT">Montana</option>
            <option value="NE">Nebraska</option>
            <option value="NV">Nevada</option> 
            <option value="NO">Other</option>
        </select>

        <!-- Add show only if choice is other functionality -->
        <label for="other">If you selected, Other please specify which state you are from.</label>
        <textarea id="other" rows="4" placeholder="Your answer..."></textarea>

        <label for="max_matches">What are you maximum matches?</label>
        <textarea id="max_matches" name="max_matches" rows="1" placeholder="Your answer..."></textarea>

        <label>What Industries do you have experience in?</label>
        <textarea name="industries" rows="4" placeholder="Your answer..."></textarea>

        <label>What are your job titles?</label>
        <textarea name="job_titles" rows="4" placeholder="Your answer..."></textarea>

        <label>Which professional development skills would you like to grow during this mentorship?</label>
        <textarea name="industries_professional" rows="4" placeholder="Your answer..."></textarea>

        <label>What are your personal interests outside of work or school?</label>
        <textarea name="interests_personal" rows="4" placeholder="Your answer..."></textarea>

        <label>Which industry/ies would you like to learn more about?</label>
        <textarea name="industries" rows="4" placeholder="Your answer..."></textarea>

        <label>What are your custom interests outside of work or school?</label>
        <textarea name="custom_cohort" rows="4" placeholder="Your answer..."></textarea>
    </div>

    <button type="submit" id="submit-button" style="display: none;">Submit</button>
</form>
<p id="demo">Hello world!</p>
<p id="api">Works?</p>
<script>
    function showAdditionalQuestions() {
        const role = document.getElementById("role").value;
        const menteeQuestionsDiv = document.getElementById("mentee-questions");
        const mentorQuestionDiv = document.getElementById("mentor-question");
        const submitButton = document.getElementById("submit-button");

        if (role === "mentee") {
            menteeQuestionsDiv.style.display = "block"; // Show mentee questions
            mentorQuestionDiv.style.display = "none"; // Hide mentor question
            submitButton.style.display = "inline"; // Show submit button
        } else if (role === "mentor") {
            menteeQuestionsDiv.style.display = "none"; // Hide mentee questions
            mentorQuestionDiv.style.display = "block"; // Show mentor question
            submitButton.style.display = "inline"; // Show submit button
        } else {
            menteeQuestionsDiv.style.display = "none"; // Hide mentee questions
            mentorQuestionDiv.style.display = "none"; // Hide mentor question
            submitButton.style.display = "none"; // Hide submit button
        }
    };

    document.getElementById("userForm").addEventListener("submit", function (event) {
            event.preventDefault(); // Prevent actual form submission
            console.log("Form submitted!");
            // Get form elements
            const role = document.getElementById("role").value;
            const fullName = document.querySelector('textarea[name="fullName"]').value;
            const cohort_names = document.querySelector('select[name="program"]').value;
            const job_titles = document.querySelector('textarea[name="job_titles"]').value;
            const industries = document.querySelector('textarea[name="industries"]').value;

            const interests_personal = document.querySelector('textarea[name="interests_personal"]').value;
            const interests_professional = document.querySelector('textarea[name="industries_professional"]').value;
            const custom_cohort = document.querySelector('textarea[name="custom_cohort"]').value;
            const ethnicity = document.querySelector('select[name="ethnicity"]').value;
            const gender = document.querySelector('select[name="gender"]').value;
            const pronouns = document.querySelector('textarea[name="pronouns"]').value;
            let max_matches = 0;
            let name_type = null;

            if (role === "mentor") { // mentor role
                max_matches = document.querySelector('textarea[name="max_matches"]').value;
                name_type = "mentor_name"
            } else { // mentee role
                max_matches = 1;
                name_type = "mentee_name"
            }


            const jsondata = 
            {
                "body": [
                    {
                        [name_type] : fullName,
                        "pronouns": pronouns,
                        "job_titles": job_titles,
                        "industries": industries,
                        "interests_personal": interests_personal,
                        "interests_professional": interests_professional,
                        "custom_cohort": custom_cohort,
                        "ethnicity": ethnicity,
                        "gender": gender,
                        "max_matches": max_matches,
                        "cohort_names": cohort_names
                    }
                ]
            }
            console.log(jsondata);

            const jsonString = JSON.stringify(jsondata, null, 2); // Convert jsondata to a string for display

            const resultdiv = document.getElementById("demo")
            resultdiv.innerHTML = jsonString;
            callLambda(jsonString);
    });

    async function callLambda(jsonString){
    try {
        const response = await fetch('https://k44x7yofmh.execute-api.us-west-1.amazonaws.com/default/', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: jsonString
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const new_data = JSON.stringify(data, null, 2);
        console.log(new_data);
        const result = document.getElementById("api");
        result.innerHTML = new_data;
        return data;
    } catch (error) {
        // This will catch network errors, parsing errors, and any errors thrown manually
        console.error("Error calling Lambda:", error.message);
        // You can also rethrow the error if needed
        // throw error;
        }
    };

</script>

</body>
</html>
<!--
Error Response:
{ "statusCode": 400, "headers": { "Content-Type": "application/json", 
 "Access-Control-Allow-Headers": [ "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token" ], 
 "Access-Control-Allow-Methods": [ "OPTIONS,PUT" ], 
 "Access-Control-Allow-Credentials": [ true ], 
 "Access-Control-Allow-Origin": [ "*" ], 
 "X-Requested-With": [ "*" ] }, 
 "body": "{\"message\": \"No body provided\"}" }
-->