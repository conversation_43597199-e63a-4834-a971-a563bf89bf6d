<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preferences Form</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px; /* Increase space between form groups */
        }
        label {
            display: block; /* Make labels block elements */
            margin: 10px 0; /* Add margin above and below labels */
        }
        textarea {
            width: 100%; /* Full width for textareas */
            padding: 10px; /* Add padding inside textareas */
            margin-top: 5px; /* Space above textareas */
            border: 1px solid #ccc; /* Border for textareas */
            border-radius: 4px; /* Rounded corners */
        }
        #mentee-questions {
            display: none; /* Hide initially */ 
        }
    </style>
</head>
<body>

<h1>Preferences Form</h1>

<div class="form-group">
    <label for="name">Name</label>
    <textarea id="name" rows="1" placeholder="(first and last)"></textarea>

    <label for="program">Which program are you participating in?</label>
    <select id="program" style="width: 100%;">
        <option value="">Select a program</option>
        <option value="engineers">Engineers</option>
        <option value="designers">Designers</option>
    </select>

    <label for="user-types">Are you a mentee or a mentor?</label>
    <select id="user-types" onchange="showAdditionalQuestions()">
        <option value="">Select an option</option>
        <option value="mentee">Mentee</option>
        <option value="mentor">Mentor</option> 
    </select>
</div>

<div class="form-group" id="mentee-questions">
    <label for="job-titles">Please list job role(s) you're interested in.</label>
    <small>Just the job title is great! This will help us pair you with mentors with similar professional interests or journeys as you.</small>
    <textarea id="job-titles" rows="1" placeholder="Your answer..."></textarea>

    <!-- UNFINISHED: add fetching from Google Sheet-->
    <label>Which industry/ies would you like to learn more about?</label>
    <div class="input-container">
        <input type="text" id="industry-input" placeholder="Type to search...">
        <div id="suggestions" class="suggestions"></div>
    </div>
    <div id="selected-industries" class="chips-container"></div>
    <script src="script.js"></script>
</div>

<div class="form-group" id="mentor-questions" style="display: none;">
    <label for="reason">Why do you want to mentor?</label>
    <textarea id="reason" rows="4" placeholder="Your answer..."></textarea>
    
    <label for="state">Where are you from?</label> <!-- Question with Searchable Dropdown! -->
    <select id="state" style="width: 100%;">
        <option value="">Select a state</option>
        <option value="AL">Alabama</option>
        <option value="AK">Alaska</option>
        <option value="AZ">Arizona</option>
        <option value="AR">Arkansas</option>
        <option value="CA">California</option>
        <option value="CO">Colorado</option>
        <option value="CT">Connecticut</option>
        <option value="DE">Delaware</option>
        <option value="FL">Florida</option>
        <option value="GA">Georgia</option>
        <option value="HI">Hawaii</option>
        <option value="ID">Idaho</option>
        <option value="IL">Illinois</option>
        <option value="IN">Indiana</option>
        <option value="IA">Iowa</option>
        <option value="KS">Kansas</option>
        <option value="KY">Kentucky</option>
        <option value="LA">Louisiana</option>
        <option value="ME">Maine</option>
        <option value="MD">Maryland</option>
        <option value="MA">Massachusetts</option>
        <option value="MI">Michigan</option>
        <option value="MN">Minnesota</option>
        <option value="MS">Mississippi</option>
        <option value="MO">Missouri</option>
        <option value="MT">Montana</option>
        <option value="NE">Nebraska</option>
        <option value="NV">Nevada</option> 
        <option value="NO">Other</option>
    </select>

    <!-- Add show only if choice is other functionality -->
    <label for="other">If you selected, Other please specify which state you are from.</label>
    <textarea id="other" rows="4" placeholder="Your answer..."></textarea>

</div>

<button type="submit" id="submit-button" style="display: none;">Submit</button>
<script src="script.js"></script>

</body>
</html>
