import clsx from "clsx";
import { OpalDescriptionLabelText } from "../OpalDescriptionLabelText";

export type FillInFieldProps = {
    id?: string;
    name?: string;
    labelText?: string;
    placeholder?: string;
    minLength?: number;
    maxLength?: number;
    isRequired?: boolean;
    defaultValue?: string;
    value?: string;
    onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
    onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
    onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
}

export const FillInField = ({
    id,
    name,
    labelText,
    placeholder,
    minLength,
    maxLength,
    isRequired,
    defaultValue,
    value,
    onChange,
    onBlur,
    onFocus
}: FillInFieldProps) => {
    return(
        <div style={{display: "flex", flexDirection: "column"}}>
            {!!labelText && <OpalDescriptionLabelText className="pb-2" text={labelText} />}
            <input
                type="text"
                id={id}
                name={name}
                placeholder={placeholder}
                required={isRequired}
                minLength={minLength}
                maxLength={maxLength}
                defaultValue={defaultValue}
                value={value}
                onChange={onChange}
                onBlur={onBlur}
                onFocus={onFocus}
                className="border border-black rounded-[22.5px] h-[45px] w-full px-6 
                           bg-superlightgray border-gray // Base background color
                           focus:bg-verylightblue focus:border-blue hover:bg-superlightgray hover:border-darkgray" // Focus and hover styles
            />
        </div>
    )
}