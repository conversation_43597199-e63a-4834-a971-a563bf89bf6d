'use client';
import React from 'react'
import Select, { ActionMeta, GroupBase, MultiValue, OptionsOrGroups } from 'react-select'

type MultiSelectProps = {
    className: string;
    options: OptionsOrGroups<string, GroupBase<string>>;
    value?: string[];
    onChange?: ((newValue: MultiValue<string>, actionMeta: ActionMeta<string>) => void) | undefined
}

export const MultiSelect = ({className, options, value, onChange}: MultiSelectProps) => (
    <Select
        isMulti
        hideSelectedOptions={false}  // This keeps selected options visible
        className={className}
        options={options}
        value={value}
        onChange={onChange}
        styles={{
            control: (providedStyles, state) => ({
              ...providedStyles,
              borderTopLeftRadius: "22.5px",
              borderTopRightRadius: "22.5px",
              borderBottomLeftRadius: state.menuIsOpen ? '0px' : '22.5px',
              borderBottomRightRadius: state.menuIsOpen ? '0px' : '22.5px',
              borderWidth: '1px',
              borderColor: 'black'
            }),
            menu: (providedStyles) => ({ // Target the menu to reset position and shape the edges
                ...providedStyles,
                marginTop: '0px',
                position: "unset",
                borderTopLeftRadius: '0px',
                borderTopRightRadius: '0px',
                borderBottomLeftRadius: '22.5px',
                borderBottomRightRadius: '22.5px',
                borderWidth: '1px',
                borderColor: 'black'
            }),
            menuList: (providedStyles) => ({  // Target the menu list to fix the last menu item radius
                ...providedStyles,
                paddingTop: '0px',
                paddingBottom: '0px',
                "& > :last-of-type": { // Apply ONLY to last menu item
                    borderBottomLeftRadius: "22.5px",
                    borderBottomRightRadius: "22.5px",
                },
            }),
            option: (providedStyles, state) => ({
                ...providedStyles,
                backgroundColor: state.isSelected ? '#F5F3FF' : state.isFocused ? '#e8e8e8' : '#FAFAFA', // Use correct hex for selected state
                color: 'black',
                borderWidth: '1px',
                borderColor: 'gray',
            }),
            multiValue: (providedStyles) => ({
                ...providedStyles,
                backgroundColor: '#3923B1',
                borderRadius: '22.5px',
                color: 'white'
            }),
            multiValueLabel: (providedStyles) => ({
                ...providedStyles,
                color: 'white'
            }),
        }}
    />
)