'use client';
import React, { useState } from 'react';
import clsx from 'clsx';

type RadioSetProps = {
  options: string[];
  value?: string;
  onChange?: (option: string) => void;
  className?: string;
  isRequired?: boolean;
  showError?: boolean;
};

export const RadioSet = ({options, value, onChange, className = '', isRequired = false, showError = false, }: RadioSetProps) => {
  const [selectedOption, setSelectedOption] = useState<string | undefined>(value);

  return (
    <div
      className={clsx('flex flex-col gap-8', className,
        // added red border to wrapper if required, error shown, and nothing selected
        isRequired && showError && !selectedOption && 'border-red')}
    >
      <div className="flex flex-row gap-8">
        {options.map((option, index) => (
          <button
            key={index}
            onClick={() => {
              setSelectedOption(option); 
              if (onChange) onChange(option); 
            }}
            className={clsx(
              'flex-1 px-5 py-6 border rounded-[22.5px] h-28',
              selectedOption === option
                ? 'bg-verylightblue hover:bg-verylightblue border-blue'
                : 'border-gray hover:bg-superlightgray hover:border-darkgray',
              isRequired && showError && !selectedOption && 'border-red hover:border-red'
            )}
          >
            {option}
          </button>
        ))}
      </div>

      {/*added to  validation error message if required but nothing is selected (same as checkbox)*/}
      {isRequired && showError && !selectedOption && (
        <span className="text-red text-sm mt-2">This field is required.</span>
      )}
    </div>
  );
};
