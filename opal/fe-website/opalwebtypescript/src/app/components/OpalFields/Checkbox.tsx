'use client'; // MUST be first line in client components

import React, { useState } from 'react';
import clsx from 'clsx';

// https://stackoverflow.com/questions/63216291/make-checkbox-field-mandatory-using-react
// https://stackoverflow.com/questions/43484500/how-to-set-checkbox-properties-in-typescript

type CheckboxProps = {
  options: string[]; 
  values?: string[]; 
  onChange?: (values: string[]) => void; //callback to parent if/when selection changes
  className?: string; 
  isRequired?: boolean; 
  showError?: boolean; 
};

export const Checkbox = ({
  options,
  values = [], 
  onChange,
  className = '',
  isRequired = false,
  showError = false,
}: CheckboxProps) => {
  const [selectedOptions, setSelectedOptions] = useState<string[]>(values);

  // handles when client either clicks/or doesnt click on it to update the form
  const toggleOption = (option: string) => {
      //new string to hold updated boxes that are checked
    let updatedOptions: string[];
      //if option is selected, remove it
    if (selectedOptions.includes(option)) {
        updatedOptions = selectedOptions.filter((v) => v !== option);
    } else {
        //otherwise, add it to the selection options
        updatedOptions = [...selectedOptions, option];
    }
    setSelectedOptions(updatedOptions);

    if (onChange) {
      onChange(updatedOptions);
    }
  };
  return (
    <div
      className={clsx(
        'flex flex-col gap-8', 
        className,
        showError && 'border-red' // to add red border if there is an error
      )}
    >
      {options.map((option, index) => (
        <button
          key={index}
          type="button"
          onClick={() => toggleOption(option)}
          className={clsx(
            'px-5 py-6 border border-gray rounded-[22.5px] h-28',
             selectedOptions.includes(option)
             // condition ? valueIfTrue : valueIfFalse (if)
             // if true
             ? 'bg-verylightblue hover:bg-verylightblue border-blue'
             // if false
             : 'border-gray hover:bg-superlightgray hover:border-darkgray',
             isRequired && showError && selectedOptions.length === 0 && 'border-red hover:bg-superlightgray hover:border-red'
          )}
        >
          {option}
        </button>
      ))}

      {/* show the error message if it is not clicked on */}
      {isRequired && showError && selectedOptions.length === 0 && (
        <span className="text-red text-sm mt-2">This field is required.</span>
      )}
    </div>
  );
};
