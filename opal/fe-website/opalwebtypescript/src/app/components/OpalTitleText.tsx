import React from 'react';

import OpalText, { OpalTextProps } from '../primitives/OpalText';

type OpalTitleTextProps = OpalTextProps & {
  isRequired?: boolean;
};

// Header enforces specific fontSize
export const OpalTitleText = (props: OpalTitleTextProps) => {
  return (
    <div style={{display: "flex", flexDirection: "row"}}>
      <OpalText {...props} fontSize='15px' />
      <OpalText color="orange" text="*" />
    </div>
  );
};

