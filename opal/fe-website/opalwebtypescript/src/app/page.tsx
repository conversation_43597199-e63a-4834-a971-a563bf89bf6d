'use client';
import { useState } from 'react';
import Image from "next/image";

import {OpalTitleText as OpalFormHeader} from './components/OpalTitleText';
import {OpalDescriptionLabelText as OpalFormDescription} from './components/OpalDescriptionLabelText';
import { FillInField } from "./components/OpalFields/FillIn";
import { RadioSet } from "./components/OpalFields/RadioSet";
import { Checkbox } from "./components/OpalFields/Checkbox";
import { MultiSelect } from "./components/OpalFields/MultiSelect";
import { Submit } from "./components/OpalFields/Submit";

// IMPORTANT: Remember to add iframe resize script to <head> of compiled output - <script src="https://cdn.jsdelivr.net/npm/iframe-resizer@4.3.6/js/iframeResizer.contentWindow.min.js"></script>

const multiSelectOptions = [
  "Accounting",
  "Administration / Business Support / Waste Management Services",
  "Agriculture / Forestry / Fishing / Hunting",
  "Aerospace / Airlines / Aviation",
  "Apparel / Fashion",
].map(option => ({value: option, label: option}))

export default function Home() {
  //checkboxes 
  const [selectedCheckboxes, setSelectedCheckboxes] = useState<string[]>([]);
  const [formError, setFormError] = useState<string | null>(null);

  //radio 
  const [selectedRadio, setSelectedRadio] = useState<string | undefined>();
  const [radioError, setRadioError] = useState<string | null>(null);

  // handle checkbox change
  const handleCheckboxChange = (values: string[]) => {
    setSelectedCheckboxes(values); //selected checkboxes
    // clear the error message when the user selects a checkbox
    if (values.length > 0) {
      setFormError(null);
    }
  };

  //handle radiobox change
  const handleRadioChange = (value: string) => {
    //update radio value
    setSelectedRadio(value);
    if (value) {
      //clear error once selected
      setRadioError(null);
    }
  };

   // submit form 
  const handleSubmit = () => {
    let isValid = true;

    //checkbox
    if (selectedCheckboxes.length === 0) {
      alert("Please select an option!");
      setFormError("No checkboxes were selected.");  
    } else {
      alert(selectedCheckboxes.join('\n'));
      setFormError(null);  
    }

    //radiobox
    if (!selectedRadio) {
      setRadioError("Please select a program.");
      isValid = false;
    } else {
      setRadioError(null);
    }

    if (isValid) {
      alert( `Program: ${selectedRadio}\n` + `Selected Preferences:\n${selectedCheckboxes.join('\n')}`);
    }
  }; 
  return (
    <div className="App">
      <MainGrid>
        {/* form starts here  */}

        {/* submit form(???) */}
         <form onSubmit={(e) => {
            e.preventDefault(); // default page (stops it from reloading)
            handleSubmit();     // existing logic 
          }}
          className="contents"  > 

        <OpalFormHeader className="col-span-6 pt-10 pb-2" text="Name" isRequired />
        <OpalFormDescription className="col-span-6 pb-4" text="Please include your name as shown within the Slack Community." />
        <div className="col-span-6 grid md:grid-cols-2 gap-4 md:gap-8 pb-10">
          <FillInField labelText="First Name" />
          <FillInField labelText="Last Name" />
          
        </div>

        <OpalFormHeader className="col-span-6 pb-2" text="Which program are you participating in?" isRequired />
        <RadioSet className="col-span-6 pb-10" options={["Software Engineers April 2025", "Product Designers April 2025", "Product Managers April 2025"]}
         // for change in radioset
         value={selectedRadio}
         onChange={handleRadioChange}
         isRequired={true}
         showError={!!radioError}/>

        <OpalFormHeader className="col-span-6 pb-2" text="Please list the job roles you’re interested in." isRequired />
        <MultiSelect
          className="col-span-6 pb-10"
          options={multiSelectOptions}
        />

        <OpalFormHeader className="col-span-6 pb-2" text="If you'd like for Race & Ethnicity to be a factor in the matching process, please select your preferences. Please select all the apply." />
        <div className="col-span-6 pb-4">
          <OpalFormDescription className="inline font-bold" text="If you have no preference, please leave this question blank. " />
          <OpalFormDescription className="inline" text="We can't guarantee you'll be matched with someone who aligns perfectly with your preference. We will not share this data with your mentor" />
        </div>
        <Checkbox
          className="col-span-6 pb-10"
          options={[
            "Indigenous or Alaska Native (e.g. Navajo Nation / Blackfeet Tribe / Inupiat Traditional Gov't. etc.)",
            "Asian or Asian American (e.g. Chinese / Japanese / Filipino / Korean / South Asian / Vietnamese etc.)",
            "Black or African American (e.g. Jamaican / Nigerian / Haitian / Ethiopian / etc.)",
            "East Asian: Chinese / Japanese / Korean / Okinawan / Taiwanese / Tibetan",
            "Hispanic or Latino/a (e.g. Puerto Rican / Mexican / Cuban / Salvadoran / Colombian / etc.)"
          ]}
          onChange={handleCheckboxChange}
          isRequired={true}
          // !! =  boolean makes it a true/false
          showError={!!formError}
        />
        <Submit className="col-span-1 pb-1" options={['SUBMIT']} />
         </form> 

        {/* form ends here */}
      </MainGrid> 
    </div>
  );
}

const MainGrid = ({children}: {children: React.ReactNode}) => (
  <div
    className="grid grid-cols-8 m-1"
    style={{
      borderColor: 'black',
      borderWidth: '1px',
      borderRadius: '50px'
    }}
  >
    <div className="col-start-2 col-span-6 grid grid-cols-subgrid gap-4">
        {children}
    </div>
  </div>
)

 // eslint-disable-next-line @typescript-eslint/no-unused-vars
 function originalHome() {
  return (
    <div className="grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20 font-[family-name:var(--font-geist-sans)]">
      <main className="flex flex-col gap-8 row-start-2 items-center sm:items-start">
        <Image
          className="dark:invert"
          src="/next.svg"
          alt="Next.js logo"
          width={180}
          height={38}
          priority
        />
        <ol className="list-inside list-decimal text-sm text-center sm:text-left font-[family-name:var(--font-geist-mono)]">
          <li className="mb-2">
            Get started by editing{" "}
            <code className="bg-black/[.05] dark:bg-white/[.06] px-1 py-0.5 rounded font-semibold">
              src/app/page.tsx
            </code>
            .
          </li>
          <li>Save and see your changes instantly.</li>
        </ol>

        <div className="flex gap-4 items-center flex-col sm:flex-row">
          <a
            className="rounded-full border border-solid border-transparent transition-colors flex items-center justify-center bg-foreground text-background gap-2 hover:bg-[#383838] dark:hover:bg-[#ccc] text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5"
            href="https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Image
              className="dark:invert"
              src="/vercel.svg"
              alt="Vercel logomark"
              width={20}
              height={20}
            />
            Deploy now
          </a>
          <a
            className="rounded-full border border-solid border-black/[.08] dark:border-white/[.145] transition-colors flex items-center justify-center hover:bg-[#f2f2f2] dark:hover:bg-[#1a1a1a] hover:border-transparent text-sm sm:text-base h-10 sm:h-12 px-4 sm:px-5 sm:min-w-44"
            href="https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
            target="_blank"
            rel="noopener noreferrer"
          >
            Read our docs
          </a>
        </div>
      </main>
      <footer className="row-start-3 flex gap-6 flex-wrap items-center justify-center">
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/file.svg"
            alt="File icon"
            width={16}
            height={16}
          />
          Learn
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/window.svg"
            alt="Window icon"
            width={16}
            height={16}
          />
          Examples
        </a>
        <a
          className="flex items-center gap-2 hover:underline hover:underline-offset-4"
          href="https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app"
          target="_blank"
          rel="noopener noreferrer"
        >
          <Image
            aria-hidden
            src="/globe.svg"
            alt="Globe icon"
            width={16}
            height={16}
          />
          Go to nextjs.org →
        </a>
      </footer>
    </div>
  );
}
