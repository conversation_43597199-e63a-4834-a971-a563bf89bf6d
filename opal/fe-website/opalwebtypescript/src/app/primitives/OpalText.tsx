import React from 'react';

export type OpalTextProps =  {
  className?: string;
  text: string;
  styleOverrides?: React.CSSProperties;
  color?: string;
  fontSize?: string;
  fontFamily?: string;
  lineHeight?: number;
};

// Custom Text Component
const OpalText: React.FC<OpalTextProps> = ({
  className,
  text,
  styleOverrides,
  color,
  fontSize,
  fontFamily,
  lineHeight
}) => {
  // You can combine the passed styles with defaults
  const textStyle = {
    color: color || 'black', // Default to black if no color is provided
    fontSize: fontSize || '12px', // Default to 16px if no fontSize is provided
    fontFamily: fontFamily || "Poppins",
    lineHeight: lineHeight ||1.5,
    ...styleOverrides, // Any additional styles passed in the styleOverrides prop
  };

  return <p className={className} style={textStyle}>{text}</p>;
};

export default OpalText;