import type { Metadata } from "next";
import "@fontsource/poppins"; // Defaults to weight 400
import "@fontsource/poppins/400.css"; // Regular weight
import "@fontsource/poppins/700.css"; // Regular weight
import "@fontsource/poppins/400-italic.css"; // Specify weight and style
import "./globals.css";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`poppins antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
