import type { Config } from "tailwindcss";

export default {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    colors: {
      'superlightgray': '#fafafa', 
      'verylightblue': '#f5f3ff', 
      'lightgray': '#e8e8e8', 
      'gray': '#aeaeae', 
      'darkgray': '#7f7f7f', 
      'black': '#000000', 
      'magenta': '#901960', 
      'blue': '#3923b1', 
      'orange': '#ff5900', 
      'purple': '#73187f', 
      // 'blue': '#4228cd', // TODO: ask why we have duplicate colors in figma
      'pink': '#d90568', 
      'yellow': '#ffbd22', 
      // 'orange': '#f53b21', // TODO: ask why we have duplicate colors in figma
      'cyan-ish': '#34d8be', 
      'superdarkgray': '#4a4a4a', 
    },
    fontFamily: {
      'poppins': ['Poppins', 'sans-serif'], // Ensure Poppins is the default font
    },
    fontWeight: {
      'normal': '400',
      'bold': '700',    // Explicitly map 'bold' to the 700 weight
      // ... any other custom font weights
    }
  },
  plugins: [],
} satisfies Config;
